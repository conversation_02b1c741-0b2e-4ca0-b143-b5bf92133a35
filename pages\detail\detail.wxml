<view class="page-container">
  <!-- 文章内容区域 -->
  <view class="content-card">
    <!-- 文章头部信息 -->
    <view class="article-header">
      <view class="article-title">{{articleDetail.title}}</view>
      <view class="article-meta">
        <view class="meta-item">
          <t-icon name="user" size="small" class="meta-icon" />
          <text>{{articleDetail.createBy || '未知作者'}}</text>
        </view>
        <view class="meta-item">
          <t-icon name="time" size="small" class="meta-icon" />
          <text>{{articleDetail.createTime || '未知时间'}}</text>
        </view>
        <view class="meta-item">
          <t-icon name="view" size="small" class="meta-icon" />
          <text>{{articleDetail.views || 0}} 阅读</text>
        </view>
      </view>
    </view>
    <!-- 文章内容 -->
    <view class="article-body">
      <rich-text
        nodes="{{articleDetail.content}}"
        class="rich-text-content"
      />
    </view>
  </view>

  <!-- 评论区域 -->
  <view class="comment-card">
    <view class="comment-header">
      <t-icon
        name="message"
        color="var(--color-primary)"
        class="comment-icon"
      />
      <text class="section-title">评论（{{comments.length}}）</text>
    </view>

    <!-- 评论输入框 -->
    <view class="comment-form">
      <view class="input-container">
        <textarea
          placeholder="请输入评论内容..."
          value="{{newComment}}"
          bindinput="handleNewCommentInput"
          class="comment-input"
          placeholder-class="placeholder-style"
          auto-height
        />
        <t-button
          bindtap="handlePublishComment"
          theme="primary"
          size="small"
          icon="arrowup"
          shape="circle"
          variant="text"
          class="send-button"
        />
      </view>
    </view>

    <!-- 评论列表 -->
    <view
      wx:for="{{comments}}"
      wx:key="id"
      class="comment-item"
    >
      <t-avatar
        class="comment-avatar"
        image="{{item.avatar}}"
        shape="circle"
      />

      <view class="comment-main">
        <view class="comment-header">
          <view class="user-info">
            <text class="user-name">{{item.nickName}}</text>
            <text class="comment-time">{{item.date}}</text>
          </view>
          <t-button
            bindtap="handleReplyClick"
            data-id="{{item.id}}"
            theme="text"
            size="small"
            class="reply-button"
          >
            回复
            <t-icon name="right" size="12" />
          </t-button>
        </view>

        <text class="comment-content">{{item.content}}</text>

        <!-- 回复输入框 -->
        <view
          wx:if="{{isReplying && replyToId === item.id}}"
          class="reply-form"
        >
          <view class="input-container">
            <textarea
              placeholder="回复@{{item.nickName}}..."
              value="{{replyContent}}"
              bindinput="handleReplyContentChange"
              class="reply-input"
              auto-height
              placeholder-class="placeholder-style"
            />
            <view class="action-buttons">
              <t-button
                bindtap="handleReplyCancel"
                theme="default"
                size="small"
                class="cancel-btn"
              >取消</t-button>
              <t-button
                bindtap="handlePublishComment"
                theme="primary"
                size="small"
                class="confirm-btn"
              >发送</t-button>
            </view>
          </view>
        </view>

        <!-- 子评论 -->
        <view wx:if="{{item.replies}}" class="sub-comments">
          <view
            wx:for="{{item.replies}}"
            wx:key="id"
            class="sub-comment-item"
          >
            <t-avatar
              image="{{item.avatar}}"
              size="64rpx"
              class="sub-comment-avatar"
            />
            <view class="sub-comment-content">
              <text class="reply-info">
                <text class="author-name">{{item.nickName}}</text>
                <text class="reply-to">回复</text>
                <text class="target-name">{{item.replyToNickName || item.replyToAuthor}}</text>
              </text>
              <text class="sub-comment-text">{{item.content}}</text>
            </view>
          </view>
        </view>
      </view>   <!-- .comment-main -->
    </view>     <!-- .comment-item -->
  </view>       <!-- .comment-card -->
</view>         <!-- .page-container -->