/* pages/gpt/gpt.wxss */
page {
  background-color: #f5f5f5;
  margin: 0;
  padding: 0;
  height: 100vh;
}

/* 页面容器 */
.page-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e0c3fc 100%);
  box-sizing: border-box;
  padding-bottom: 100rpx; /* 预留底部输入区空间 */
}

/* 欢迎界面容器 */
.welcome-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  background-color: #f5f5f5;
}

.welcome-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

/* AI图标容器 */
.ai-icon-container {
  margin-bottom: 60rpx;
}

.ai-icon {
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #87CEEB, #B0E0E6);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(135, 206, 235, 0.3);
}

.ai-text {
  font-size: 48rpx;
  font-weight: bold;
  color: white;
  letter-spacing: 2rpx;
}

/* 欢迎标题 */
.welcome-title {
  font-size: 48rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.welcome-subtitle {
  font-size: 32rpx;
  color: #666;
  line-height: 1.5;
}

/* 聊天容器 */
.chat-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  overflow: hidden;
  background: #f5f5f5;
}

.message-scroll-view {
  flex: 1;
  padding: 30rpx 20rpx 0 20rpx;
  overflow-y: auto;
  box-sizing: border-box;
}

/* 消息样式 */
.message {
  margin-bottom: 40rpx;
}

.message:last-child {
  margin-bottom: 20rpx;
}

/* AI消息容器 */
.ai-message-container {
  display: flex;
  flex-direction: row;
  align-items: flex-end;
  margin-bottom: 30rpx;
}

.ai-avatar-small {
  width: 72rpx;
  height: 72rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #e0c3fc 0%, #8ec5fc 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  flex-grow: 0;
  box-sizing: border-box;
  overflow: hidden;
  margin-right: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(128,197,252,0.12);
}

.ai-text-small {
  font-size: 28rpx;
  font-weight: bold;
  color: white;
  line-height: 1;
  text-align: center;
  white-space: nowrap;
}

.ai-message-content {
  background: #fff;
  color: #222;
  margin-left: 16rpx;
  border-radius: 28rpx;
  font-size: 32rpx;
  padding: 28rpx 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.06);
  max-width: 80vw;
  word-break: break-all;
}

.ai-message-content::before {
  content: '';
  position: absolute;
  left: -12rpx;
  top: 20rpx;
  width: 0;
  height: 0;
  border-top: 12rpx solid transparent;
  border-bottom: 12rpx solid transparent;
  border-right: 12rpx solid white;
}

/* 用户消息容器 */
.user-message-container {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: flex-end;
  margin-bottom: 30rpx;
}

.user-message-content {
  background: linear-gradient(135deg, #e0c3fc 0%, #8ec5fc 100%);
  color: #fff;
  margin-right: 16rpx;
  border-radius: 28rpx;
  font-size: 32rpx;
  padding: 28rpx 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(128,197,252,0.12);
  max-width: 80vw;
  word-break: break-all;
}

.user-message-content::after {
  content: '';
  position: absolute;
  right: -12rpx;
  top: 20rpx;
  width: 0;
  height: 0;
  border-top: 12rpx solid transparent;
  border-bottom: 12rpx solid transparent;
  border-left: 12rpx solid #007AFF;
}

/* 用户头像 */
.user-avatar {
  width: 72rpx;
  height: 72rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
  margin-left: 12rpx;
  background: linear-gradient(135deg, #ffdde1 0%, #ee9ca7 100%);
  box-shadow: 0 2rpx 8rpx rgba(238,156,167,0.12);
}

.user-text {
  font-size: 28rpx;
  font-weight: bold;
  color: white;
}

/* 输入容器 */
.input-container {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background: #fff;
  padding: 16rpx 24rpx 32rpx 24rpx;
  box-shadow: 0 -2rpx 12rpx rgba(0,0,0,0.04);
  z-index: 999;
  border-top-left-radius: 32rpx;
  border-top-right-radius: 32rpx;
}

.input-wrapper {
  display: flex;
  align-items: center;
  background: #fff;
  border-radius: 40rpx;
  padding: 8rpx 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(128,197,252,0.08);
}

.input-field {
  flex: 1;
  border: none;
  outline: none;
  font-size: 32rpx;
  background: transparent;
  color: #333;
  padding: 16rpx 0;
}

.input-field::placeholder {
  color: #999;
}

.send-button {
  margin-left: 16rpx;
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #e0c3fc 0%, #8ec5fc 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx rgba(128,197,252,0.18);
  border: none;
  outline: none;
  transition: background 0.2s, box-shadow 0.2s;
}

.send-button .send-text {
  font-size: 36rpx;
  font-weight: bold;
  color: #fff;
  line-height: 1;
}

.send-button-inactive {
  background: linear-gradient(135deg, #e0c3fc 0%, #8ec5fc 100%);
  opacity: 0.4;
  color: #fff;
  box-shadow: none;
}

.send-button-active {
  background: linear-gradient(135deg, #e0c3fc 0%, #8ec5fc 100%);
  opacity: 1;
  color: #fff;
  box-shadow: 0 4rpx 16rpx rgba(128,197,252,0.18);
}

.send-button:active {
  opacity: 0.85;
}

/* 加载状态 */
.loading-dots {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 10rpx 0;
}

.loading-dot {
  width: 8rpx;
  height: 8rpx;
  border-radius: 50%;
  background: #999;
  animation: loadingBounce 1.4s ease-in-out infinite both;
}

.loading-dot:nth-child(1) { animation-delay: -0.32s; }
.loading-dot:nth-child(2) { animation-delay: -0.16s; }
.loading-dot:nth-child(3) { animation-delay: 0s; }

@keyframes loadingBounce {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .input-container {
    padding-bottom: 48rpx;
  }

  .ai-message-content,
  .user-message-content {
    max-width: 480rpx;
  }

  .welcome-title {
    font-size: 48rpx;
  }

  .welcome-subtitle {
    font-size: 28rpx;
  }

  .ai-icon {
    width: 160rpx;
    height: 160rpx;
  }

  .ai-text {
    font-size: 48rpx;
  }
}

/* 滚动条美化 */
::-webkit-scrollbar {
  width: 0;
  background: transparent;
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .welcome-content {
    background: rgba(45, 55, 72, 0.95);
    border-color: rgba(255, 255, 255, 0.1);
  }

  .welcome-title {
    color: #f7fafc;
  }

  .welcome-subtitle {
    color: #e2e8f0;
  }

  .ai-message-content {
    background: rgba(45, 55, 72, 0.95);
    border-color: rgba(255, 255, 255, 0.1);
  }

  .ai-message-content text {
    color: #f7fafc;
  }

  .input-container {
    background: rgba(45, 55, 72, 0.95);
    border-top-color: rgba(255, 255, 255, 0.1);
  }

  .input-wrapper {
    background: rgba(74, 85, 104, 0.9);
    border-color: rgba(102, 126, 234, 0.3);
  }

  .input-field {
    color: #f7fafc;
  }

  .input-field::placeholder {
    color: #a0aec0;
  }
}