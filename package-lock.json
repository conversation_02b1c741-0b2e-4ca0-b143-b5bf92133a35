{"name": "psl12-phc", "lockfileVersion": 3, "requires": true, "appid": "wxb009bc93d19d3f4d", "cloudfunctionTemplateRoot": "cloudfunctionTemplate/", "compileType": "miniprogram", "description": "网络安全知识科普小程序", "libVersion": "trial", "projectname": "网安智解小程序", "simulatorType": "wechat", "packages": {}, "condition": {}, "editorSetting": {"tabIndent": "auto", "tabSize": 2}, "packOptions": {"ignore": [], "include": []}, "setting": {"autoAudits": false, "bundle": false, "checkInvalidKey": true, "condition": true, "coverView": true, "disableUseStrict": false, "enableEngineNative": false, "enhance": true, "es6": true, "ignoreUploadUnusedFiles": true, "lazyloadPlaceholderEnable": false, "minified": true, "minify": true, "minifyWXML": true, "minifyWXSS": true, "newFeature": true, "nodeModules": false, "packNpmManually": false, "packNpmRelationList": [], "postcss": true, "preloadBackgroundData": false, "showES6CompileOption": false, "showShadowRootInWxmlPanel": true, "uglifyFileName": false, "uploadWithSourceMap": true, "useApiHook": true, "useApiHostProcess": true, "useCompilerModule": true, "useIsolateContext": true, "useMultiFrameRuntime": true, "userConfirmedBundleSwitch": false, "userConfirmedUseCompilerModuleSwitch": false, "useStaticServer": true, "babelSetting": {"disablePlugins": [], "ignore": [], "outputPath": ""}}, "simulatorPluginLibVersion": {}}